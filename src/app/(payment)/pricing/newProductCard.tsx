import React,{useContext} from "react";
import { Check } from "lucide-react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

// Add this inside the PersonalRepositorySection component
interface ProductCardProps {
  planType: string;
  productName: string;
  productDescription: string;
  credits: string;
  prices?: string[];
  selectedPrice?: string;
  onPriceSelect?: (price: string) => void;
  features: string[];
  icon?: React.ReactNode;
  onClick: () => void;
  isLoggedIn: boolean;
  currentPlan?: boolean;
  dynamicCredits: string;

  currentProductName?: string;
  Restrict: number
}

const NewProductCard = ({
  planType,
  productName,
  productDescription,
  credits,
  prices,
  selectedPrice,
  onPriceSelect,
  features,
  icon,
  onClick,
  isLoggedIn,
  currentPlan,
  dynamicCredits,

  currentProductName,
  Restrict
}: ProductCardProps) => {
  let isHighlighted = currentProductName?.toLowerCase().includes(productName.toLowerCase());

  const displayCredits = dynamicCredits || credits;

  const {showAlert} = useContext(AlertContext)
  // Convert tokens to numbers for comparison
  const planTokens = parseInt(dynamicCredits);
  
   const handleUpgradeClick = () => {
    // Check if this plan requires price selection and no price is selected
    if (prices && prices.length > 0 && !selectedPrice) {
      // You can either show an alert or pass an error callback
      showAlert('Please select a price first','info');
      return;
    }
    
    // If validation passes, proceed with the original onClick
    onClick();
  };

  return (
    <div className={`relative rounded-lg h-full border transition-all duration-200 ${isHighlighted ? 'border-primary/30 bg-gradient-to-tr from-card from-70% to-primary/10' : 'border-border bg-card'}`}>
      <div className={"flex flex-col p-6 h-full text-card-foreground"}>
        {/* Product Name and Description */}
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="font-inter text-heading-4 font-medium mb-2">
              {productName}
              {currentPlan && (<span className="mb-1 ml-3 bg-primary/20 text-primary typography-body-sm font-medium me-2 px-2.5 py-0.5 rounded-full">{currentPlan}</span>)}
            </h3>
            <p className="font-inter text-body-sm font-light min-h-12 max-w-60 text-wrap text-muted-foreground">
              {productDescription}
            </p>
          </div>
          {icon && (
            <div className={`p-2 rounded-lg transition-colors ${isHighlighted ? 'bg-primary/20' : 'bg-muted/20'
              }`}>
              {icon}
            </div>
          )}
        </div>

        {/* Tokens Display */}
        <div className="mb-6">
          <div className="flex items-baseline gap-2">
            <span className="text-heading-2 font-semibold text-foreground">{Number(displayCredits).toLocaleString()}</span>
            <span className="text-body-sm text-muted-foreground">credits/month</span>
          </div>
        </div>

        {/* Price Selection */}
        {prices && prices.length > 0 ? (
          <div className="flex flex-col gap-2 mb-6 min-h-[90px]">
            <div className="flex items-center flex-1 w-full justify-evenly gap-2 p-1.5 rounded-lg border border-border">
              {prices.map((price) => (
                <button
                  key={price}
                  onClick={() => onPriceSelect?.(price)}
                  className={`px-4 w-full py-2 rounded-lg text-body-sm font-medium transition-all duration-200 relative ${selectedPrice === price
                      ? 'bg-primary text-primary-foreground shadow-md border border-primary transform scale-105'
                      : 'hover:bg-muted/10 border border-transparent text-foreground hover:scale-102'
                    }`}
                >
                  ${price}
                  {selectedPrice === price && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border-2 border-background"></div>
                  )}
                </button>
              ))}
            </div>
            <span className="text-body-sm font-light text-center text-muted-foreground">USD/month</span>
          </div>
        ) : (
          <div className="mb-6 min-h-[90px] flex flex-col justify-center">
            <div className="flex items-baseline justify-center gap-3">
              <span className="text-heading-1 font-semibold text-foreground">Forever</span>
            </div>
          </div>
        )}
        {/* Action Button */}
        <button
          className={`w-full px-4 py-3 rounded-lg transition-all duration-200 font-medium ${planTokens <= Restrict || planType === "free"
              ? 'bg-muted/20 cursor-not-allowed text-muted-foreground'
              : planTokens > Restrict
                ? 'bg-primary hover:bg-primary/90 text-primary-foreground'
                : 'bg-muted/30 hover:bg-muted/40 text-foreground'
            }`}
          onClick={handleUpgradeClick}
          disabled={planTokens <= Restrict || planType === "free"}
        >
          {planTokens === Restrict
            ? "Current Plan"
            : planTokens < Restrict
              ? "Plan Unavailable"
              : planType === "free"
                ? "Current Plan"
                : isLoggedIn
                  ? `Upgrade to ${productName}${currentPlan ? ` ${currentPlan}` : ''}`
                  : `Choose ${productName}`}
        </button>

        {/* Features List */}
        <div className="space-y-3 mt-8">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start gap-2">
              <Check className={`w-4 h-4 mt-1 transition-colors ${isHighlighted ? 'text-primary' : 'text-muted-foreground'
                }`} />
              <span className="text-body-sm text-card-foreground">{feature}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NewProductCard;